Imports MySql.Data.MySqlClient
Imports System.Drawing

Public Class FormPaymentHistory
    Private currentUserID As Integer
    
    Public Sub New(userID As Integer)
        InitializeComponent()
        currentUserID = userID
    End Sub
    
    Private Sub FormPaymentHistory_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "Riwayat Pembayaran"
        Me.Size = New Size(1100, 750)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.BackColor = Color.FromArgb(248, 249, 250)
        Me.FormBorderStyle = FormBorderStyle.FixedSingle
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.MinimumSize = New Size(1100, 750)
        Me.MaximumSize = New Size(1100, 750)

        CreateUI()
        LoadPaymentHistory()
    End Sub
    
    Private Sub CreateUI()
        ' Header Panel dengan gradient background
        Dim pnlHeader As New Panel With {
            .Name = "pnlHeader",
            .Location = New Point(0, 0),
            .Size = New Size(Me.Width, 100),
            .BackColor = Color.FromArgb(52, 152, 219),
            .BorderStyle = BorderStyle.None,
            .Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right
        }

        Dim lblTitle As New Label With {
            .Text = "💳 RIWAYAT PEMBAYARAN",
            .Font = New Font("Segoe UI", 20, FontStyle.Bold),
            .ForeColor = Color.White,
            .Location = New Point(40, 20),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim lblSubtitle As New Label With {
            .Text = "Daftar semua transaksi pembayaran Anda",
            .Font = New Font("Segoe UI", 12),
            .ForeColor = Color.FromArgb(230, 240, 255),
            .Location = New Point(40, 55),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        pnlHeader.Controls.AddRange({lblTitle, lblSubtitle})
        Me.Controls.Add(pnlHeader)
        
        ' Filter Panel dengan styling yang lebih modern
        Dim pnlFilter As New Panel With {
            .Name = "pnlFilter",
            .Location = New Point(30, 120),
            .Size = New Size(1040, 70),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.None,
            .Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right
        }

        ' Tambahkan shadow effect
        AddHandler pnlFilter.Paint, Sub(sender, e)
            Dim rect As New Rectangle(0, 0, pnlFilter.Width - 1, pnlFilter.Height - 1)
            Using pen As New Pen(Color.FromArgb(220, 220, 220), 1)
                e.Graphics.DrawRectangle(pen, rect)
            End Using
        End Sub

        Dim lblFilter As New Label With {
            .Text = "🔍 Filter Status:",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(25, 22),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim cmbStatus As New ComboBox With {
            .Name = "cmbStatus",
            .Size = New Size(180, 35),
            .Location = New Point(170, 18),
            .Font = New Font("Segoe UI", 11),
            .DropDownStyle = ComboBoxStyle.DropDownList
        }
        cmbStatus.Items.AddRange({"Semua Status", "pending", "processing", "shipped", "success", "cancelled"})
        cmbStatus.SelectedIndex = 0
        AddHandler cmbStatus.SelectedIndexChanged, AddressOf CmbStatus_SelectedIndexChanged

        Dim btnRefresh As New Button With {
            .Text = "🔄 Refresh",
            .Size = New Size(120, 40),
            .Location = New Point(380, 15),
            .BackColor = Color.FromArgb(46, 204, 113),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        btnRefresh.FlatAppearance.BorderSize = 0
        AddHandler btnRefresh.Click, Sub() LoadPaymentHistory()

        pnlFilter.Controls.AddRange({lblFilter, cmbStatus, btnRefresh})
        Me.Controls.Add(pnlFilter)
        
        ' Payment History Panel (Scrollable) dengan styling yang lebih baik
        Dim pnlHistory As New Panel With {
            .Name = "pnlHistory",
            .Location = New Point(30, 210),
            .Size = New Size(1040, 500),
            .BackColor = Color.FromArgb(248, 249, 250),
            .BorderStyle = BorderStyle.None,
            .AutoScroll = True,
            .Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Bottom,
            .Padding = New Padding(10, 10, 10, 10)
        }

        Me.Controls.Add(pnlHistory)
    End Sub
    
    Private Sub LoadPaymentHistory(Optional statusFilter As String = "")
        Try
            koneksi()
            
            Dim pnlHistory As Panel = CType(Me.Controls("pnlHistory"), Panel)
            pnlHistory.Controls.Clear()
            
            ' Query untuk mengambil data pembayaran dengan detail produk
            Dim query As String = "SELECT o.order_id, o.order_date, o.total_amount, o.order_status, " &
                                 "o.shipping_address, o.payment_method, " &
                                 "GROUP_CONCAT(CONCAT(p.product_name, ' (', od.quantity, 'x)') SEPARATOR ', ') as products " &
                                 "FROM orders o " &
                                 "LEFT JOIN order_details od ON o.order_id = od.order_id " &
                                 "LEFT JOIN products p ON od.product_id = p.product_id " &
                                 "WHERE o.user_id = @userID"
            
            If statusFilter <> "" AndAlso statusFilter <> "Semua Status" Then
                query &= " AND o.order_status = @status"
            End If
            
            query &= " GROUP BY o.order_id ORDER BY o.order_date DESC"
            
            perintahSQL = New MySqlCommand(query, koneksiDatabase)
            perintahSQL.Parameters.AddWithValue("@userID", currentUserID)

            If statusFilter <> "" AndAlso statusFilter <> "Semua Status" Then
                perintahSQL.Parameters.AddWithValue("@status", statusFilter)
            End If

            Dim reader As MySqlDataReader = perintahSQL.ExecuteReader()
            Dim yPos As Integer = 20
            
            If Not reader.HasRows Then
                Dim lblEmpty As New Label With {
                    .Text = "📋 Belum ada riwayat pembayaran",
                    .Font = New Font("Segoe UI", 14),
                    .ForeColor = Color.FromArgb(127, 140, 141),
                    .Location = New Point(400, 200),
                    .AutoSize = True,
                    .BackColor = Color.Transparent
                }
                pnlHistory.Controls.Add(lblEmpty)
            Else
                While reader.Read()
                    Dim paymentCard As Panel = CreatePaymentCard(reader)
                    paymentCard.Location = New Point(10, yPos)
                    pnlHistory.Controls.Add(paymentCard)
                    yPos += paymentCard.Height + 20
                End While

                ' Tambahkan padding di bagian bawah agar scroll tidak terpotong
                Dim paddingPanel As New Panel With {
                    .Size = New Size(1, 50),
                    .Location = New Point(10, yPos),
                    .BackColor = Color.Transparent
                }
                pnlHistory.Controls.Add(paddingPanel)
            End If
            
            reader.Close()
            tutupKoneksi()
            
        Catch ex As Exception
            MessageBox.Show("Error loading payment history: " & ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Function CreatePaymentCard(reader As MySqlDataReader) As Panel
        Dim card As New Panel With {
            .Size = New Size(1000, 200),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.None,
            .Margin = New Padding(0, 0, 0, 15)
        }

        ' Tambahkan shadow effect dan rounded corners
        AddHandler card.Paint, Sub(sender, e)
            Dim rect As New Rectangle(0, 0, card.Width - 1, card.Height - 1)
            Using pen As New Pen(Color.FromArgb(220, 220, 220), 1)
                e.Graphics.DrawRectangle(pen, rect)
            End Using
            ' Shadow effect
            Using shadowBrush As New SolidBrush(Color.FromArgb(20, 0, 0, 0))
                e.Graphics.FillRectangle(shadowBrush, New Rectangle(2, 2, card.Width - 2, card.Height - 2))
            End Using
        End Sub

        ' Order ID dan Status - posisi rapi
        Dim lblOrderID As New Label With {
            .Text = $"📦 Order #{reader("order_id")}",
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 73, 94),
            .Location = New Point(25, 20),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim lblStatus As New Label With {
            .Text = GetStatusText(reader("order_status").ToString()),
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .ForeColor = Color.White,
            .BackColor = GetStatusColor(reader("order_status").ToString()),
            .Location = New Point(850, 20),
            .Size = New Size(120, 30),
            .TextAlign = ContentAlignment.MiddleCenter
        }
        
        ' Tanggal dan Metode Pembayaran - baris kedua
        Dim lblDate As New Label With {
            .Text = $"📅 {Convert.ToDateTime(reader("order_date")):dd/MM/yyyy HH:mm}",
            .Font = New Font("Segoe UI", 11),
            .ForeColor = Color.FromArgb(127, 140, 141),
            .Location = New Point(25, 55),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        Dim lblPayment As New Label With {
            .Text = $"💳 {reader("payment_method")}",
            .Font = New Font("Segoe UI", 11, FontStyle.Bold),
            .ForeColor = Color.FromArgb(46, 204, 113),
            .Location = New Point(300, 55),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        ' Produk - baris ketiga
        Dim lblProducts As New Label With {
            .Text = $"🛍️ Produk: {reader("products")}",
            .Font = New Font("Segoe UI", 11),
            .ForeColor = Color.FromArgb(44, 62, 80),
            .Location = New Point(25, 85),
            .Size = New Size(940, 25),
            .AutoEllipsis = True,
            .BackColor = Color.Transparent
        }

        ' Alamat - baris keempat
        Dim lblAddress As New Label With {
            .Text = $"📍 Alamat: {reader("shipping_address")}",
            .Font = New Font("Segoe UI", 11),
            .ForeColor = Color.FromArgb(44, 62, 80),
            .Location = New Point(25, 115),
            .Size = New Size(940, 25),
            .AutoEllipsis = True,
            .BackColor = Color.Transparent
        }
        
        ' Total Amount - baris kelima
        Dim lblTotal As New Label With {
            .Text = $"💰 Total: Rp {Convert.ToDecimal(reader("total_amount")):N0}",
            .Font = New Font("Segoe UI", 13, FontStyle.Bold),
            .ForeColor = Color.FromArgb(231, 76, 60),
            .Location = New Point(25, 145),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        ' Status Pembayaran Detail dengan estimasi - baris keenam
        Dim paymentStatus As String = GetPaymentStatusText(reader("order_status").ToString())
        Dim orderID As Integer = Convert.ToInt32(reader("order_id"))
        Dim estimatedTime As String = OrderStatusManager.Instance.GetEstimatedNextStatusTime(orderID)

        Dim lblPaymentStatus As New Label With {
            .Text = $"🔄 Status: {paymentStatus}",
            .Font = New Font("Segoe UI", 11),
            .ForeColor = GetStatusColor(reader("order_status").ToString()),
            .Location = New Point(500, 145),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        ' Estimasi waktu status berikutnya
        Dim lblEstimation As New Label With {
            .Text = $"⏱️ {estimatedTime}",
            .Font = New Font("Segoe UI", 10),
            .ForeColor = Color.FromArgb(127, 140, 141),
            .Location = New Point(500, 170),
            .AutoSize = True,
            .BackColor = Color.Transparent
        }

        card.Controls.AddRange({lblOrderID, lblStatus, lblDate, lblPayment, lblProducts, lblAddress, lblTotal, lblPaymentStatus, lblEstimation})

        Return card
    End Function
    
    Private Function GetStatusText(status As String) As String
        Select Case status.ToLower()
            Case "pending"
                Return "Menunggu"
            Case "processing"
                Return "Diproses"
            Case "shipped"
                Return "Dikirim"
            Case "success"
                Return "Berhasil"
            Case "cancelled"
                Return "Dibatalkan"
            Case Else
                Return status
        End Select
    End Function
    
    Private Function GetPaymentStatusText(status As String) As String
        Select Case status.ToLower()
            Case "pending"
                Return "Menunggu Pembayaran"
            Case "processing"
                Return "Pembayaran Berhasil - Sedang Diproses"
            Case "shipped"
                Return "Pembayaran Berhasil - Sedang Dikirim"
            Case "success"
                Return "Pembayaran Berhasil - Pesanan Selesai"
            Case "cancelled"
                Return "Pembayaran Dibatalkan"
            Case Else
                Return "Status Tidak Diketahui"
        End Select
    End Function
    
    Private Function GetStatusColor(status As String) As Color
        Select Case status.ToLower()
            Case "pending"
                Return Color.FromArgb(241, 196, 15)
            Case "processing"
                Return Color.FromArgb(52, 152, 219)
            Case "shipped"
                Return Color.FromArgb(155, 89, 182)
            Case "success"
                Return Color.FromArgb(46, 204, 113)
            Case "cancelled"
                Return Color.FromArgb(231, 76, 60)
            Case Else
                Return Color.Gray
        End Select
    End Function
    
    Private Sub CmbStatus_SelectedIndexChanged(sender As Object, e As EventArgs)
        Dim cmb As ComboBox = CType(sender, ComboBox)
        LoadPaymentHistory(cmb.SelectedItem.ToString())
    End Sub
End Class
