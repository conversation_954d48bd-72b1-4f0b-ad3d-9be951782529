Imports MySql.Data.MySqlClient

Module ModulKoneksi
    ' Variabel koneksi database dengan nama yang lebih jelas
    Public koneksiDatabase As MySqlConnection
    Public perintahSQL As MySqlCommand
    Public adapterData As MySqlDataAdapter
    Public pembacaData As MySqlDataReader
    Public kumpulanData As DataSet
    Public stringKoneksi As String

    Sub koneksi()
        Try
            ' String koneksi ke database MySQL
            stringKoneksi = "Server=127.0.0.1;User Id=root;Password=**************;Database=db_ecommerce"
            koneksiDatabase = New MySqlConnection(stringKoneksi)

            ' Buka koneksi jika masih tertutup
            If koneksiDatabase.State = ConnectionState.Closed Then
                koneksiDatabase.Open()
            End If
        Catch ex As Exception
            MessageBox.Show("Koneksi ke database gagal: " & ex.Message, "Error Koneksi", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Sub tutupKoneksi()
        ' Tutup koneksi jika masih terbuka
        If koneksiDatabase IsNot Nothing AndAlso koneksiDatabase.State = ConnectionState.Open Then
            koneksiDatabase.Close()
        End If
    End Sub
End Module